/**
 * PWA 相关类型定义
 */

/**
 * PWA 安装提示事件接口
 */
export interface BeforeInstallPromptEvent extends Event {
  /** 显示安装提示 */
  prompt(): Promise<void>;
  /** 用户选择结果 */
  userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
}

/**
 * PWA 支持情况检查结果
 */
export interface PWASupportInfo {
  /** 是否支持 Service Worker */
  serviceWorker: boolean;
  /** 是否支持 Web App Manifest */
  manifest: boolean;
  /** 是否在 standalone 模式下运行 */
  standalone: boolean;
  /** 是否支持安装提示事件 */
  beforeInstallPrompt: boolean;
}

/**
 * Service Worker 注册配置
 */
export interface ServiceWorkerConfig {
  /** 检测到新版本时的回调 */
  onNeedRefresh?: () => void;
  /** 离线准备就绪时的回调 */
  onOfflineReady?: () => void;
  /** 注册成功时的回调 */
  onRegistered?: (registration: ServiceWorkerRegistration) => void;
  /** 注册失败时的回调 */
  onRegisterError?: (error: Error) => void;
}

/**
 * PWA 初始化返回值
 */
export interface PWAInitResult {
  /** Service Worker 更新函数 */
  updateSW: () => Promise<void>;
  /** 获取安装提示事件 */
  getDeferredPrompt: () => BeforeInstallPromptEvent | null;
  /** 清理安装提示引用 */
  clearDeferredPrompt: () => void;
}
