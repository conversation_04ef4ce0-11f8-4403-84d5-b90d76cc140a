/**
 * PWA 功能初始化模块
 * 负责 Service Worker 注册、更新管理和安装提示处理
 */

import { registerSW } from "virtual:pwa-register";
import { pinia } from "@/stores";
import type { BeforeInstallPromptEvent, PWASupportInfo, PWAInitResult } from "./types";

/**
 * PWA 弹窗统计相关配置
 */
const STORAGE_KEY = "PWA_COUNT_STATS";
const MAX_DAILY_PROMPTS = 3;

/**
 * PWA 弹窗统计数据接口
 */
interface PromptStats {
  /** 日期 (YYYY-MM-DD 格式) */
  date: string;
  /** 当天已显示次数 */
  count: number;
}

/**
 * PWA 安装提示事件引用
 */
let deferredPrompt: BeforeInstallPromptEvent | null = null;

/**
 * Service Worker 注册配置
 * 处理应用更新、离线缓存等功能
 */
function initServiceWorker() {
  const updateSW = registerSW({
    onNeedRefresh() {
      // 检测到新版本时提示用户
      const shouldUpdate = confirm("New content is available. Do you want to refresh?");
      if (shouldUpdate) {
        updateSW();
      }
    },
    onOfflineReady() {
      console.log("✅ 应用已可离线使用");
    },
    onRegistered(registration: ServiceWorkerRegistration) {
      if (registration) {
        console.log("✅ Service Worker 注册成功");

        // 设置定期检查更新（每小时一次）
        const UPDATE_INTERVAL = 60 * 60 * 1000; // 1 hour
        setInterval(() => {
          registration.update();
        }, UPDATE_INTERVAL);
      }
    },
    onRegisterError(error: Error) {
      console.error("❌ Service Worker 注册失败：", error);
    },
  });

  return updateSW;
}

/**
 * 初始化 PWA 安装提示管理
 * 处理浏览器的安装提示事件
 */
function initInstallPrompt() {
  // 监听安装提示事件
  window.addEventListener("beforeinstallprompt", (event: Event) => {
    // 阻止浏览器默认的安装提示
    event.preventDefault();

    // 保存事件供后续使用
    deferredPrompt = event as BeforeInstallPromptEvent;

    // 通过 Pinia 存储，让组件可以访问
    (pinia as any)._pwaDeferredPrompt = deferredPrompt;

    console.log("📱 PWA 安装提示已准备就绪");
  });

  // 监听应用安装完成事件
  window.addEventListener("appinstalled", () => {
    console.log("🎉 PWA 安装成功");

    // 清理安装提示引用
    deferredPrompt = null;
    (pinia as any)._pwaDeferredPrompt = null;
  });
}

/**
 * 获取当前的安装提示事件
 */
export function getDeferredPrompt(): BeforeInstallPromptEvent | null {
  return deferredPrompt;
}

/**
 * 清理安装提示引用
 */
export function clearDeferredPrompt(): void {
  deferredPrompt = null;
  (pinia as any)._pwaDeferredPrompt = null;
}

/**
 * 初始化 PWA 功能
 * 包含 Service Worker 注册和安装提示管理
 */
export function initPWA(): PWAInitResult {
  // 初始化 Service Worker
  const updateSW = initServiceWorker();

  // 初始化安装提示管理
  initInstallPrompt();

  return {
    updateSW,
    getDeferredPrompt,
    clearDeferredPrompt,
  };
}

/**
 * 检查 PWA 支持情况
 */
export function checkPWASupport(): PWASupportInfo {
  const support: PWASupportInfo = {
    serviceWorker: "serviceWorker" in navigator,
    manifest: "manifest" in document.createElement("link"),
    standalone: window.matchMedia("(display-mode: standalone)").matches,
    beforeInstallPrompt: "onbeforeinstallprompt" in window,
  };

  return support;
}

// ==================== PWA 工具函数 ====================

/**
 * 增强版 PWA 安装状态检测
 * @returns {boolean} 是否已安装为 PWA
 */
export function isPWAInstalled(): boolean {
  // 1. 标准 display-mode 检测（覆盖多数现代浏览器）
  const isStandalone = window.matchMedia("(display-mode: standalone)").matches;
  const isFullscreen = window.matchMedia("(display-mode: fullscreen)").matches;
  const isMinimalUI = window.matchMedia("(display-mode: minimal-ui)").matches;

  // 2. iOS Safari 特有检测
  const isIOSStandalone = (window.navigator as any).standalone === true;

  // 3. 排除浏览器环境特征（可选）
  const isNotBrowser = !window.matchMedia("(display-mode: browser)").matches;

  // 满足任一 PWA 运行模式即视为已安装
  return (isStandalone || isFullscreen || isMinimalUI || isIOSStandalone) && isNotBrowser;
}

/**
 * 获取今日日期字符串
 * @returns {string} YYYY-MM-DD 格式的日期字符串
 */
function getToday(): string {
  return new Date().toISOString().split("T")[0];
}

/**
 * 创建默认统计数据
 * @param {string} date - 日期字符串
 * @returns {PromptStats} 默认统计数据
 */
function createDefaultStats(date: string): PromptStats {
  return { date, count: 0 };
}

/**
 * 获取当前统计信息，自动清理过期数据
 * @returns {PromptStats} 当前统计信息
 */
function getStats(): PromptStats {
  const today = getToday();

  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (!saved) {
      return createDefaultStats(today);
    }

    const parsed = JSON.parse(saved) as PromptStats;

    // 如果不是今天的数据，重置为今天
    if (parsed.date !== today) {
      return createDefaultStats(today);
    }

    return parsed;
  } catch (error) {
    console.warn("[PWA] 解析本地存储失败，重置统计数据:", error);
    return createDefaultStats(today);
  }
}

/**
 * 保存统计信息到本地存储
 * @param {PromptStats} stats - 统计数据
 */
function saveStats(stats: PromptStats): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(stats));
  } catch (error) {
    console.warn("[PWA] 保存统计数据失败:", error);
  }
}

/**
 * 检查是否应该显示 PWA 安装弹窗
 * @returns {boolean} 是否应该显示弹窗
 */
export function shouldShowPWAPrompt(): boolean {
  // 1. 如果已在 PWA 中运行，不显示弹窗
  if (isPWAInstalled()) {
    console.log("[PWA] 已安装，跳过弹窗");
    return false;
  }

  const stats = getStats();

  // 2. 检查每日显示次数限制
  if (stats.count >= MAX_DAILY_PROMPTS) {
    console.log(`[PWA] 今日已显示 ${stats.count} 次，达到上限`);
    return false;
  }

  console.log(`[PWA] 可以显示弹窗，今日已显示 ${stats.count}/${MAX_DAILY_PROMPTS} 次`);
  return true;
}

/**
 * 记录一次弹窗显示
 */
export function recordPWAPromptShown(): void {
  const stats = getStats();
  stats.count += 1;
  saveStats(stats);

  console.log(`[PWA] 记录弹窗显示，今日已显示 ${stats.count}/${MAX_DAILY_PROMPTS} 次`);
}

/**
 * 重置弹窗计数（调试用）
 */
export function resetPWAPromptCount(): void {
  localStorage.removeItem(STORAGE_KEY);
  console.log("[PWA] 弹窗计数已重置");
}

/**
 * 获取当前弹窗统计信息（调试用）
 * @returns {PromptStats} 当前统计信息
 */
export function getPWAPromptStats(): PromptStats {
  return getStats();
}
