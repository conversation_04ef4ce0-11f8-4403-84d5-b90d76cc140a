<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="31.4209" y="1.08423" width="85.8843" height="111.849" rx="20" transform="rotate(10 31.4209 1.08423)" fill="url(#paint0_linear_5786_1619)"/>
<mask id="mask0_5786_1619" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="15" y="4" width="98" height="119">
<rect x="31.4209" y="1.08423" width="85.8843" height="111.849" rx="20" transform="rotate(10 31.4209 1.08423)" fill="url(#paint1_linear_5786_1619)"/>
</mask>
<g mask="url(#mask0_5786_1619)">
<g style="mix-blend-mode:overlay" filter="url(#filter0_f_5786_1619)">
<path d="M96.3047 12.5246C107.183 14.4426 114.447 24.816 112.529 35.6939L100.052 106.452C98.1342 117.33 87.7609 124.593 76.8832 122.675L31.6955 114.707C20.8177 112.789 13.5544 102.416 15.4724 91.5382L27.9489 20.7801C29.867 9.9023 40.2402 2.63889 51.118 4.55695L96.3047 12.5246ZM50.9426 5.54045C40.6086 3.71829 30.7541 10.6185 28.932 20.9525L16.4551 91.7125C14.6329 102.046 21.5331 111.901 31.8671 113.723L77.0567 121.691C87.3907 123.513 97.2452 116.613 99.0674 106.279L111.544 35.5193C113.366 25.1853 106.466 15.3308 96.1322 13.5086L50.9426 5.54045Z" fill="white"/>
</g>
<g filter="url(#filter1_i_5786_1619)">
<rect x="31.4209" y="1.08423" width="77.8951" height="105.857" rx="20" transform="rotate(10 31.4209 1.08423)" fill="url(#paint2_linear_5786_1619)"/>
</g>
<g opacity="0.6" filter="url(#filter2_f_5786_1619)">
<ellipse cx="21.7099" cy="56.1593" rx="4.99327" ry="45.9381" transform="rotate(10 21.7099 56.1593)" fill="#FFFF8C"/>
</g>
<g opacity="0.8" filter="url(#filter3_f_5786_1619)">
<ellipse cx="97.4391" cy="69.5121" rx="1.99731" ry="45.9381" transform="rotate(10 97.4391 69.5121)" fill="#FFFF8C"/>
</g>
<g opacity="0.5" filter="url(#filter4_f_5786_1619)">
<path d="M93.0285 100.132C90.6833 110.468 80.6076 117.247 70.0559 115.387L32.7361 108.806C21.8582 106.888 14.5948 96.5152 16.5129 85.6373L27.9489 20.7804C29.867 9.90254 40.2402 2.63913 51.118 4.55719L88.4378 11.1377C98.991 12.9987 106.141 22.8177 104.807 33.3349C106.047 23.3613 99.2591 14.0602 89.2491 12.2952L51.9274 5.71435C41.5935 3.89226 31.7389 10.7924 29.9168 21.1264L18.4804 85.9852C16.6583 96.3191 23.5585 106.174 33.8924 107.996L71.2142 114.577C81.2224 116.341 90.7807 109.925 93.0285 100.132Z" fill="white"/>
</g>
<g filter="url(#filter5_iii_5786_1619)">
<path d="M86.0633 18.9769C92.5701 21.1144 96.6882 27.7628 95.4643 34.7047L84.7191 95.6436C83.3765 103.258 76.1151 108.342 68.5007 107L37.0657 101.457C29.4514 100.114 24.3669 92.853 25.7095 85.2386L36.4547 24.2998C37.6787 17.3589 43.8208 12.5199 50.665 12.7352C49.9855 17.0288 52.8692 21.0916 57.1675 21.8495L76.8367 25.3177C81.1349 26.0756 85.2334 23.2439 86.0633 18.9769ZM84.179 18.4999L52.6017 12.9319C52.6254 12.936 52.6493 12.9393 52.6731 12.9435L84.108 18.4864C84.1318 18.4905 84.1553 18.4956 84.179 18.4999Z" fill="url(#paint3_linear_5786_1619)"/>
</g>
<g filter="url(#filter6_di_5786_1619)">
<circle cx="60.5862" cy="59.972" r="21.9704" transform="rotate(10 60.5862 59.972)" fill="url(#paint4_linear_5786_1619)"/>
</g>
<g style="mix-blend-mode:overlay" filter="url(#filter7_f_5786_1619)">
<circle cx="60.5857" cy="59.9721" r="20.9718" transform="rotate(10 60.5857 59.9721)" fill="url(#paint5_linear_5786_1619)"/>
</g>
<g filter="url(#filter8_ddii_5786_1619)">
<path d="M65.2037 49.0699L60.0342 48.1584C58.9464 47.9666 57.9091 48.6929 57.7173 49.7807L55.9996 59.5223L52.0958 58.8339C51.0179 58.6439 49.9899 59.3636 49.7998 60.4416C49.5832 61.6704 49.9577 62.9294 50.8106 63.8399L57.4635 70.9418C58.0711 71.5905 59.0341 71.7603 59.827 71.3586L68.6549 66.8857C69.6751 66.3688 70.3924 65.4017 70.591 64.2754C70.7653 63.2872 70.1055 62.3449 69.1173 62.1707L65.0509 61.4537L66.826 51.3868C67.0178 50.299 66.2915 49.2617 65.2037 49.0699Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_5786_1619" x="12.165" y="1.24976" width="103.671" height="124.732" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.5" result="effect1_foregroundBlur_5786_1619"/>
</filter>
<filter id="filter1_i_5786_1619" x="16.2051" y="4.25" width="88.7617" height="115.444" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.909538 0 0 0 0 0.780545 0 0 0 1 0"/>
<feBlend mode="multiply" in2="shape" result="effect1_innerShadow_5786_1619"/>
</filter>
<filter id="filter2_f_5786_1619" x="4.33789" y="2.91064" width="34.7441" height="106.497" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_5786_1619"/>
</filter>
<filter id="filter3_f_5786_1619" x="81.2217" y="16.2705" width="32.4355" height="106.483" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_5786_1619"/>
</filter>
<filter id="filter4_f_5786_1619" x="14.2061" y="2.25" width="92.7607" height="115.444" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_5786_1619"/>
</filter>
<filter id="filter5_iii_5786_1619" x="21.4941" y="8.72827" width="78.1855" height="98.4866" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.945098 0 0 0 0 0.831373 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5786_1619"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.946078 0 0 0 0 0.831494 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_5786_1619" result="effect2_innerShadow_5786_1619"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.945098 0 0 0 0 0.831373 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5786_1619" result="effect3_innerShadow_5786_1619"/>
</filter>
<filter id="filter6_di_5786_1619" x="32.6123" y="31.998" width="59.9482" height="59.948" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.772956 0 0 0 0 0.66774 0 0 0 1 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_5786_1619"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5786_1619" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.749695 0 0 0 0 0.374238 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5786_1619"/>
</filter>
<filter id="filter7_f_5786_1619" x="37.6104" y="36.9968" width="45.9502" height="45.9504" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_5786_1619"/>
</filter>
<filter id="filter8_ddii_5786_1619" x="45.7393" y="44.1277" width="27.8799" height="30.4468" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.999892 0 0 0 0 0.53725 0 0 0 0 0.197072 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5786_1619"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.688616 0 0 0 0 0.27184 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_5786_1619" result="effect2_dropShadow_5786_1619"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_5786_1619" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.945098 0 0 0 0 0.831373 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_5786_1619"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.945098 0 0 0 0 0.831373 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_5786_1619" result="effect4_innerShadow_5786_1619"/>
</filter>
<linearGradient id="paint0_linear_5786_1619" x1="74.3631" y1="1.08423" x2="74.3631" y2="112.934" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFAE34"/>
<stop offset="1" stop-color="#F9824F"/>
</linearGradient>
<linearGradient id="paint1_linear_5786_1619" x1="74.3631" y1="1.08423" x2="74.3631" y2="112.934" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFAB5C"/>
<stop offset="1" stop-color="#EC7A4E"/>
</linearGradient>
<linearGradient id="paint2_linear_5786_1619" x1="70.3684" y1="1.08423" x2="70.3684" y2="106.942" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFDA74"/>
<stop offset="1" stop-color="#FF9945"/>
</linearGradient>
<linearGradient id="paint3_linear_5786_1619" x1="69.3317" y1="16.2939" x2="53.7361" y2="104.741" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFF6E9"/>
</linearGradient>
<linearGradient id="paint4_linear_5786_1619" x1="60.5861" y1="38.0016" x2="60.5861" y2="81.9424" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17E"/>
<stop offset="1" stop-color="#FF8E47"/>
</linearGradient>
<linearGradient id="paint5_linear_5786_1619" x1="60.5857" y1="39.0003" x2="60.5857" y2="63.4674" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFEC8"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
