<template>
  <ZActionSheet v-model="showPWAInstallTip" :onClose="handleClose">
    <div class="pwa-install-guide" v-if="isAvailable">
      <div class="content">
        <NuStar2 class="nustar"></NuStar2>
        <div class="description">
          We would like to add it to your home screen for the latest free bets and bonus updates.
        </div>
      </div>
    </div>
    <template #footer>
      <!-- <ZButton type="default" @click="handleClose" class="theme-primary btn">
          <ZIcon type="icon-app" color="#fff" :size="24"></ZIcon>
          <span class="btn-text">APP</span>
        </ZButton>
        <ZButton type="primary" @click="installPWA" class="theme-default btn">
          <ZIcon type="icon-web" color="#ac1140" :size="26"></ZIcon>
          <span class="btn-text">WEB-APP</span>
        </ZButton> -->
      <ZButton type="primary" @click="installPWA" class="theme-primary btn" v-if="isAvailable">
        <ZIcon type="icon-web" color="#fff" :size="26"></ZIcon>
        <span class="btn-text">WEB-APP</span>
      </ZButton>
      <div v-else></div>
    </template>
    <div v-if="!isAvailable" class="not-available">
      <div v-if="isSafari()">Tap Safari's bottom button, then select "Add to Home Screen".</div>
      <div v-else>Open the menu and select "Add to Home Screen".</div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { recordPWAPromptShown, getDeferredPrompt, clearDeferredPrompt } from "@/utils/pwa";
import NuStar2 from "@/assets/NuStar2.svg";
import { isSafari } from "@/utils/managers/MobileWindowManager";

/**
 * PWA 安装引导组件
 * 处理 PWA 安装提示和用户交互
 */

// Store 管理
const autoPopMgrStore = useAutoPopMgrStore();
const { showPWAInstallTip } = storeToRefs(autoPopMgrStore);
const isAvailable = ref(true);

// PWA 安装提示事件引用
const deferredPrompt = ref<any>(null);

/**
 * 处理 PWA 安装
 * 触发浏览器的安装提示
 */
const installPWA = async (): Promise<void> => {
  if (!deferredPrompt.value) {
    console.warn("[PWA] 安装提示不可用，可能浏览器不支持或已安装");
    // closePWADialog();
    isAvailable.value = false;
    return;
  }
  isAvailable.value = true;
  try {
    console.log("[PWA] 开始触发安装提示");

    // 触发浏览器安装提示
    await deferredPrompt.value.prompt();

    // 等待用户选择
    const choiceResult = await deferredPrompt.value.userChoice;

    console.log("[PWA] 用户选择结果:", choiceResult.outcome);

    if (choiceResult.outcome === "accepted") {
      console.log("✅ 用户接受了 PWA 安装");
    } else {
      console.log("❌ 用户拒绝了 PWA 安装");
    }
  } catch (error) {
    console.error("[PWA] 安装过程出错:", error);
  } finally {
    // 无论成功、失败还是出错，都要清理和关闭
    console.log("[PWA] 清理安装提示并关闭弹窗");
    cleanupInstallPrompt();
    closePWADialog();
  }
};

/**
 * 处理关闭按钮点击
 */
const handleClose = (): void => {
  closePWADialog();
};

/**
 * 关闭 PWA 对话框
 */
const closePWADialog = (): void => {
  console.log("[PWA] 正在关闭弹窗...");
  console.log("[PWA] 当前弹窗状态:", autoPopMgrStore.showPWAInstallTip);

  autoPopMgrStore.showPWAInstallTip = false;
  AutoPopMgr.destroyCurrentPopup();

  console.log("[PWA] 弹窗已关闭，新状态:", autoPopMgrStore.showPWAInstallTip);
};

/**
 * 清理安装提示引用
 */
const cleanupInstallPrompt = (): void => {
  deferredPrompt.value = null;
  clearDeferredPrompt();
};

/**
 * 组件挂载时的初始化
 */
onMounted(() => {
  // 从 PWA 模块获取安装提示事件
  deferredPrompt.value = getDeferredPrompt();

  console.log("[PWA] 组件已挂载，当前安装提示状态:", !!deferredPrompt.value);
});

/**
 * 监听弹窗显示状态，记录显示次数
 */
watch(
  () => autoPopMgrStore.showPWAInstallTip,
  (isVisible: boolean) => {
    if (isVisible) {
      recordPWAPromptShown();
      console.log("[PWA] 弹窗已显示，记录统计数据");
    }
  }
);
</script>

<style scoped lang="scss">
.not-available {
  padding-top: 40px;
  font-family: Inter;
  font-size: 16px;
}
.btn {
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px; /* 150% */

  :deep(.van-button__text) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  &.theme-default {
    color: #ac1140;
    background-color: #fff;
    border: 2px solid #ac1140;
  }
  &.theme-primary {
    color: #fff;
    background-color: #ac1140;
  }
}

.pwa-install-guide {
  .content {
    display: flex;
    align-items: center;
    margin-top: 16px;
    gap: 12px;
    .nustar {
      width: 60px;
    }
    .description {
      flex: 1;
      color: #222;
      text-align: left;
      font-family: Inter;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
</style>
