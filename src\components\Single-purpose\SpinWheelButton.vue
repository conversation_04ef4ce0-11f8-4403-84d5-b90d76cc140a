<template>
  <!-- 单一特定使用的组件： 大转盘的浮动按钮 -->
  <div class="turntable-float" @click="autoPopMgrStore.openSpinWheel">
    <img
      class="win-title float-animate"
      src="@/assets/images/turnTableFloat/win-title.png"
      alt="WIN YOUR DAY"
      draggable="false"
    />
    <div class="turntable-box">
      <img
        class="turntable-bg-light scale-animate"
        src="@/assets/images/turnTableFloat/g.png"
        alt=""
        draggable="false"
      />
      <img
        class="turntable-bg"
        src="@/assets/images/turnTableFloat/turntable-bg.png"
        alt="turntable"
        draggable="false"
      />
      <img
        class="turntable-windmill rotate-animate"
        src="@/assets/images/turnTableFloat/lpnr.png"
        alt="turntable"
        draggable="false"
      />
      <img
        class="turntable-spin"
        src="@/assets/images/turnTableFloat/spin-btn.png"
        alt="Spin"
        draggable="false"
      />

      <img
        class="turntable-hand scale-animate"
        src="@/assets/images/turnTableFloat/hand.png"
        alt="hand"
        draggable="false"
      />
      <div class="turntable-count" v-if="isLogin">
        <img src="@/assets/images/turnTableFloat/count-bg.png" alt="count" draggable="false" />
        <span>{{ spinInfo.left_times }}</span>
      </div>
    </div>
    <div class="turntable-amount">
      <span class="icon" v-if="isLogin">₱</span>
      <XNumberRoller :value="spinInfo.total_prize" v-if="isLogin" textColor="#ffd700" />
      <div v-else>--</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";
import { getRandomInt } from "@/utils/core/tools";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useGlobalStore } from "@/stores/global";

const autoPopMgrStore = useAutoPopMgrStore();
const globalStore = useGlobalStore();

const isLogin = computed(() => !!globalStore.token);
// 是否显示转盘弹窗
const { spinInfo } = storeToRefs(autoPopMgrStore);

let timer: ReturnType<typeof setInterval> | null = null;
// 获取剩余次数，以及total_prize，并设置定时器定时累计
const getTimesInfo = async () => {
  timer = setInterval(() => {
    spinInfo.value.total_prize = Number(spinInfo.value.total_prize) + Number(getRandomInt());
  }, 2000);
};
onMounted(() => {
  getTimesInfo();
});
onBeforeUnmount(() => {
  clearInterval(timer);
  timer = null;
});
</script>

<style lang="scss" scoped>
.turntable-float {
  position: relative;
  max-height: 100px;
  max-width: 100px;
  min-width: 100px;
  right: 4px;
  border-radius: 100px;

  // PC端拖拽优化
  user-select: none; // 防止文本选择
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  // 防止图片拖拽和右键菜单
  img {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    pointer-events: none; // 图片不响应鼠标事件
    -webkit-user-drag: none; // Webkit浏览器禁用拖拽
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
  }

  // 容器本身需要响应点击和拖拽事件
  pointer-events: auto;
}

.win-title {
  width: 45px;
  position: relative;
  bottom: -24px;
  right: -35%;
  z-index: 7;
}

.turntable-box {
  position: relative;
  max-width: 100px;
  min-height: 70px;

  .turntable-bg-light {
    width: 100px;
    height: auto;
    position: absolute;
    top: -12px;
    right: -8px;
  }

  .turntable-bg {
    width: 80px;
    display: block;
    position: absolute;
    top: 0;
    right: 0;
  }

  .turntable-windmill {
    position: absolute;
    top: 8px;
    right: 11px;
    width: 58px;
  }

  .turntable-spin {
    position: absolute;
    top: 38px;
    right: 24px;
    width: 26px;
    height: 26px;
    transform: translate(-15%, -60%);
    z-index: 2;
  }

  .turntable-hand {
    position: absolute;
    top: 32px;
    right: 24px;
    width: 20px;
    z-index: 3;
  }
}

.turntable-count {
  position: absolute;
  left: 14px;
  top: 14px;
  width: 26px;
  height: 26px;
  text-align: center;
  font-size: 10px;
  z-index: 4;
}

.turntable-count img {
  width: 100%;
}

.turntable-count span {
  position: absolute;
  left: 0;
  top: -4px;
  width: 100%;
  height: 100%;
  line-height: 36px;
  color: #fff;
  font-weight: bold;
  font-size: 12px;
  text-shadow: -1px -1px 0 #000, /* 左上 */ 1px -1px 0 #000, /* 右上 */ -1px 1px 0 #000,
    /* 左下 */ 1px 1px 0 #000;
  /* 右下 */
}

.turntable-amount {
  position: relative;
  top: -16px;
  right: -14px;
  width: 100px;
  height: 24px;
  text-align: center;
  background: url("@/assets/images/turnTableFloat/amount-bg.png") no-repeat;
  background-size: cover;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  transform: scale(0.8);
  color: #ffd700;

  .icon {
    color: #ffd700;
  }

  .icon {
    position: relative;
    z-index: 1;
    font-size: 12px;
    text-shadow: 1px 1px 2px #000;
  }
}

/* 动态循环放大缩小动画 */
.scale-animate {
  animation: scalePulse 1.2s infinite ease-in-out;
}

@keyframes scalePulse {
  0% {
    transform: scale(1);
    // opacity: 1;
  }

  50% {
    transform: scale(1.3);
    // opacity: 0.85;
  }

  100% {
    transform: scale(1);
    // opacity: 1;
  }
}

/* 新增：上下浮动动画 */
.float-animate {
  animation: floatUpDown 1.2s infinite ease-in-out;
}

@keyframes floatUpDown {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0);
  }
}

.rotate-animate {
  animation: rotateWindmill 1.6s linear infinite;
}

// 旋转动画
@keyframes rotateWindmill {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
