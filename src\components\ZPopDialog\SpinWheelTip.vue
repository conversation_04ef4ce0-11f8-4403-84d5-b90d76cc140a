<template>
  <!-- 大转盘 -->
  <ZPopOverlay :show="showSpinWheelTip">
    <div class="turntable-wrap">
      <div class="turntable-head">
        <!-- 顶部类型标题背景图 -->
        <img class="head-bg" src="@/assets/images/turnTable/turntable-head.png" alt="" />
        <!-- 顶部类型标题 -->
        <img class="type-text" :src="turnTableTargetData.headImg" alt="" />
      </div>
      <div class="turntable-content">
        <!-- 大转盘背景图 -->
        <img src="@/assets/images/turnTable/turntable-bg.png" alt="" />
        <!-- 转盘可转动部分 每格旋转 +72deg  -->
        <div class="rotate-img" :style="{ transform: `rotateZ(${targetResult.rotateDeg}deg)` }">
          <span
            :class="`rotate-num rotate-num${index + 1}`"
            v-for="(it, index) in turnTableTargetData.spin_activity_prize_config || []"
            :key="it.sort"
            v-show="index < 5"
            >₱{{ formatNumberToThousands(it.prize, { precision: 0 }) }}</span
          >
        </div>
        <img class="rotate-active" src="@/assets/images/turnTable/checked.png" alt="" />
        <!-- 点击转动按钮Spin -->
        <img
          @click="getSpinResult"
          v-if="canSpinable"
          class="rotate-btn"
          src="@/assets/images/turnTable/center-btn.png"
          alt=""
        />
        <!-- disabled的按钮Spin -->
        <img
          v-else
          class="rotate-btn"
          @click="spinDisableClick"
          src="@/assets/images/turnTable/center-btn-disable.png"
          alt=""
        />
        <!-- 顶部指向箭头 -->
        <img class="rotate-arrow" src="@/assets/images/turnTable/arrow.png" alt="" />
        <div class="turntable-progress">
          <div
            class="progress"
            v-if="canSpinable"
            :style="{
              width: `${turnTableTargetData.progressInfo.percentage}%`,
            }"
          ></div>
          <div class="progress-text">
            {{ progressText }}
          </div>
        </div>
        <div class="turntable-rollNum">
          <div>
            <div class="rollNum-title">Total Prize</div>
            <!-- 滚动数字 -->
            <div class="rollNum-comp">
              ₱
              <XNumberRoller :value="spinInfo.total_prize" textColor="#00f570" />
            </div>
          </div>
          <div>
            <!-- 滚动字幕 中奖记录 -->
            <XTextRoller :list="rollingRecords" />
          </div>
        </div>
      </div>
      <!-- 底部切换抽奖类型 根据betAmount判断是否可点击 -->
      <div class="turntable-btns">
        <div
          v-for="(it, idx) in btnsNum"
          :class="[
            `foot-btn btn${idx + 1}`,
            { active: turnTableTargetData.spin_activity_id === it.spin_activity_id },
          ]"
          @click="() => btnsClick(it)"
          :key="it.spin_activity_id"
        >
          ₱{{ formatNumberToThousands(it.maxPrize, { precision: 0 }) }}
        </div>
        <!-- 底部切换抽奖类型 遮罩层 -->
        <div class="btns-overLay">
          <div
            v-for="(it, idx) in btnsNum"
            :class="`foot-btn btn${idx + 1}`"
            @click="() => btnsClick(it)"
            :key="it.spin_activity_name"
          ></div>
        </div>
      </div>
      <span class="turntable-close" @click="handleClose">
        <ZIcon type="icon-guanbi2" :size="30" />
      </span>
    </div>
  </ZPopOverlay>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import { storeToRefs } from "pinia";
import { showToast } from "vant";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { useGlobalStore } from "@/stores/global";
import { useAutoPopMgrStore, POP_FORM } from "@/stores/autoPopMgr";
import { useDialog } from "@/components/ZDialog/dialog";
import { formatNumberToThousands } from "@/utils/core/tools";
import MathUtils from "@/utils/core/math";
import {
  getActivitySpinReceiveInfo,
  spinResult as apiSpinResult,
  getActivitySpinConfigReceiveRecordPlatform,
} from "@/api/activity";
import XTextRoller from "@/components/Single-purpose/XTextRoller.vue";
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";

// ==================== 类型定义 ====================
interface SpinPrizeConfig {
  spin_activity_prize_config_id: number | string;
  prize: string | number;
  sort: number;
}

interface SpinActivity {
  sort: number | string;
  spin_activity_id: number | string;
  spin_activity_name: string;
  spin_activity_prize_config: SpinPrizeConfig[];
  daily_betting_amount: number;
}

interface SpinData {
  spin_activity: SpinActivity[];
  current_spin_activity_sort: number;
  is_first_recharge: boolean;
  bet_amount: number;
}

interface TurntableData {
  spin_activity_prize_config: SpinPrizeConfig[];
  spin_activity_id?: number | string;
  headImg?: string;
  progressInfo?: {
    percentage: number;
    hasBet: number;
    nextBet: number;
    nextSpinName: string;
  };
}

interface SpinResult {
  targetIdx: number;
  targetSpinId?: number | string;
  rotateDeg: number;
}

interface ButtonData {
  sort: number | string;
  spin_activity_id: number | string;
  spin_activity_name: string;
  daily_betting_amount: number;
  maxPrize: string | number;
}

// ==================== Store 和状态管理 ====================
const autoPopMgrStore = useAutoPopMgrStore();
const globalStore = useGlobalStore();
const { showSpinWheelTip, spinInfo, sourceSpinWheel } = storeToRefs(autoPopMgrStore);

// 初始化 Dialog
const Dialog = useDialog();

// ==================== 响应式数据 ====================
const spinData = ref<SpinData | null>(null);
const turntableData = ref<TurntableData>({
  spin_activity_prize_config: [],
});
const spinResult = ref<SpinResult>({
  targetIdx: -1, // 初始化为 -1，表示还没有抽奖结果
  rotateDeg: 0,
});
const canSpin = ref(false);
const rollingRecords = ref<any[]>([]);
const buttonList = ref<ButtonData[]>([]);

const activityEnded = ref(false);

// ==================== 图片资源管理 ====================
const imageModules = import.meta.glob("@/assets/images/turnTable/types/*.png", {
  eager: true,
});

const imageList = Object.entries(imageModules).map(([path, module]) => ({
  name: (path.split("/").pop() || "").split(".").shift() || "",
  url: (module as { default: string }).default,
}));

// ==================== 计算属性 ====================
const canSpinable = computed(() => canSpin.value);

const targetResult = computed(() => spinResult.value);

const turnTableTargetData = computed(() => turntableData.value);

const btnsNum = computed(() => buttonList.value);

// 根据 Cocos 逻辑计算进度文本
const progressText = computed(() => {
  // 如果有抽奖结果，显示奖品金额
  if (
    spinResult.value.targetIdx >= 0 &&
    turntableData.value.spin_activity_prize_config.length > 0
  ) {
    const prizeConfig = turntableData.value.spin_activity_prize_config[spinResult.value.targetIdx];
    if (prizeConfig) {
      return `₱${formatNumberToThousands(prizeConfig.prize, { precision: 0 })}`;
    }
  }

  if (!spinData.value) return "";

  const data = spinData.value;
  const currentActivity = getCurrentActivity();
  if (!currentActivity) return "";

  // 检查首充状态
  if (!data.is_first_recharge) {
    return "Unlock this Spin by making First Deposit.";
  }

  // 检查是否已用完所有次数
  if (data.current_spin_activity_sort >= 5) {
    return "Spins all used up today. Try your luck again tomorrow!";
  }

  // 检查投注金额
  if (data.bet_amount < currentActivity.daily_betting_amount) {
    const gap = MathUtils.subtract(currentActivity.daily_betting_amount, data.bet_amount);
    return `Bet ₱${formatNumberToThousands(gap, { precision: 0 })} to start ${
      currentActivity.spin_activity_name
    }!`;
  }

  // 可以抽奖的状态
  if (data.bet_amount >= spinData.value.spin_activity[4]?.daily_betting_amount) {
    return "All the spins have been obtained, to use them!";
  }

  return `Please click the spin to start ${currentActivity.spin_activity_name}!`;
});

// ==================== 工具函数 ====================
/**
 * 获取每个活动中sort值最大的配置对应的奖品
 */
const getMaxPrizeConfigs = (activities: SpinActivity[]): ButtonData[] => {
  return activities.map((activity) => {
    const maxSortConfig = activity.spin_activity_prize_config.reduce((prev, curr) => {
      return Number(curr.sort) > Number(prev.sort) ? curr : prev;
    });

    return {
      sort: activity.sort,
      spin_activity_id: activity.spin_activity_id,
      spin_activity_name: activity.spin_activity_name,
      daily_betting_amount: activity.daily_betting_amount,
      maxPrize: maxSortConfig.prize,
    };
  });
};

/**
 * 根据活动名称获取对应的头部图片
 */
const getHeadImage = (activityName: string): string => {
  const imageName = activityName.replace(/\s/g, "");
  return imageList.find((img) => img.name === imageName)?.url || "";
};

/**
 * 计算进度信息
 */
const calculateProgress = (
  betAmount: number,
  currentActivity: SpinActivity,
  allActivities: SpinActivity[]
) => {
  if (betAmount < currentActivity.daily_betting_amount) {
    // 当前活动未达到要求
    const gap = MathUtils.subtract(currentActivity.daily_betting_amount, betAmount);
    return {
      percentage: MathUtils.divide(betAmount, currentActivity.daily_betting_amount),
      hasBet: betAmount,
      nextBet: currentActivity.daily_betting_amount,
      nextSpinName: currentActivity.spin_activity_name,
      message: `Bet ₱${formatNumberToThousands(gap, { precision: 0 })} to start ${
        currentActivity.spin_activity_name
      }!`,
    };
  } else {
    // 当前活动已达到要求
    if (betAmount >= allActivities[4]?.daily_betting_amount) {
      // 已达到最高级别
      return {
        percentage: 1,
        hasBet: betAmount,
        nextBet: currentActivity.daily_betting_amount,
        nextSpinName: currentActivity.spin_activity_name,
        message: "All the spins have been obtained, to use them!",
      };
    } else {
      // 可以进行当前级别的抽奖
      return {
        percentage: 1,
        hasBet: betAmount,
        nextBet: currentActivity.daily_betting_amount,
        nextSpinName: currentActivity.spin_activity_name,
        message: `Please click the spin to start ${currentActivity.spin_activity_name}!`,
      };
    }
  }
};

const popEndTips = (data): void => {
  if (data && data.code) {
    if (data?.code == 500100900 && !activityEnded.value) {
      activityEnded.value = true;
      Dialog({
        message: "The activity has ended!",
        showCancelButton: false,
        onConfirm: () => {
          activityEnded.value = false;
          // 关闭弹窗
          handleClose();
        },
      });
    } else if (data?.code != 500100900 && data?.code != 0) {
      showToast("Failed to obtain information.");
    }
  }
};

/**
 * 初始化转盘数据
 */
const initData = async (): Promise<void> => {
  try {
    // 并行加载数据
    await Promise.all([loadSpinRecords(), loadSpinData()]);
  } finally {
  }
};

/**
 * 加载转盘配置数据
 */
const loadSpinData = async (): Promise<void> => {
  const response = await getActivitySpinReceiveInfo();
  popEndTips(response);
  if (response?.code === 0) {
    const data = response?.data as unknown as SpinData;
    spinData.value = data;

    // 获取当前活动（限制索引范围）
    const currentIndex = Math.min(Math.max(data.current_spin_activity_sort, 0), 4);
    const currentActivity = data.spin_activity[currentIndex];

    if (!currentActivity) {
      throw new Error("Current activity not found");
    }

    // 设置转盘显示数据
    updateTurntableData(currentActivity, data);
    // 设置底部按钮
    updateButtonList(data.spin_activity);
    // 检查是否可以抽奖
    updateSpinAvailability(data, currentActivity);
  }
};

/**
 * 更新转盘显示数据
 */
const updateTurntableData = (activity: SpinActivity, data: SpinData): void => {
  turntableData.value = {
    ...activity,
    headImg: getHeadImage(activity.spin_activity_name),
    progressInfo: calculateProgress(data.bet_amount, activity, data.spin_activity),
  };
};

/**
 * 更新底部按钮列表
 */
const updateButtonList = (activities: SpinActivity[]): void => {
  const maxPrizeList = getMaxPrizeConfigs(activities);
  buttonList.value = maxPrizeList.sort((a, b) => Number(a.sort) - Number(b.sort));
};

/**
 * 更新抽奖可用性（根据 Cocos 逻辑）
 */
const updateSpinAvailability = (data: SpinData, currentActivity: SpinActivity): void => {
  // 首先检查是否首充
  if (!data.is_first_recharge) {
    canSpin.value = false;
    return;
  }

  // 检查当前排序是否超过限制
  if (data.current_spin_activity_sort >= 5) {
    canSpin.value = false;
    return;
  }

  // 检查投注金额是否足够
  if (data.bet_amount < currentActivity.daily_betting_amount) {
    canSpin.value = false;
    return;
  }

  // 检查是否还有剩余次数
  if (spinInfo.value.left_times <= 0) {
    canSpin.value = false;
    return;
  }

  // 所有条件满足，可以抽奖
  canSpin.value = true;

  // 检查是否已获得所有奖励
  // const maxActivity = data.spin_activity[4];
  // if (maxActivity && data.bet_amount >= maxActivity.daily_betting_amount) {
  //   showToast("All the spins have been obtained, to use them!");
  // }
};
/**
 * 生成随机整数
 */
const getRandomInt = (min: number, max: number): number => {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * 生成模拟抽奖记录（与 Cocos 逻辑一致）
 */
const generateTempRecords = (): Array<{
  user_id: string;
  prize_amount: string;
  prize_time: string;
}> => {
  const currentDate = new Date();
  const currentDateStr = `${currentDate.getFullYear()}-${
    currentDate.getMonth() + 1
  }-${currentDate.getDate()}`;

  // 从本地存储获取今日的模拟数据
  const name1Key = `SPIN_TEMP_ITEM_NAME1_${currentDateStr}`;
  const time1Key = `SPIN_TEMP_ITEM_TIME1_${currentDateStr}`;
  const name2Key = `SPIN_TEMP_ITEM_NAME2_${currentDateStr}`;
  const time2Key = `SPIN_TEMP_ITEM_TIME2_${currentDateStr}`;

  let name1 = localStorage.getItem(name1Key);
  let time1 = localStorage.getItem(time1Key);
  let name2 = localStorage.getItem(name2Key);
  let time2 = localStorage.getItem(time2Key);

  // 如果今日没有生成过，则生成新的随机数据
  if (!name1) {
    name1 = `30***${getRandomInt(100, 990)}`;
    localStorage.setItem(name1Key, name1);
  }

  if (!time1) {
    time1 = `00:${getRandomInt(10, 59).toString().padStart(2, "0")}`;
    localStorage.setItem(time1Key, time1);
  }

  if (!name2) {
    name2 = `31***${getRandomInt(100, 990)}`;
    localStorage.setItem(name2Key, name2);
  }

  if (!time2) {
    time2 = `00:${getRandomInt(10, 59).toString().padStart(2, "0")}`;
    localStorage.setItem(time2Key, time2);
  }

  // 返回对象数组格式的模拟记录
  return [
    {
      user_id: name1,
      prize_amount: "500",
      prize_time: time1,
    },
    {
      user_id: name2,
      prize_amount: "100",
      prize_time: time2,
    },
  ];
};

/**
 * 加载抽奖记录
 */
const loadSpinRecords = async (): Promise<void> => {
  const response = await getActivitySpinConfigReceiveRecordPlatform();
  if (response) {
    const data = response as unknown as {
      list: Array<{ user_id: string; prize_amount: string; prize_time: string }>;
      total_prize: number;
    };
    const realRecords = data.list || [];

    // 如果真实记录数量不足，使用模拟记录补充（与 Cocos 逻辑一致）
    if (realRecords.length === 0) {
      // 没有记录，使用模拟记录
      rollingRecords.value = generateTempRecords();
    } else if (realRecords.length <= 2) {
      // 记录太少，使用真实记录（Cocos 中的 showTempList2 逻辑）
      rollingRecords.value = realRecords;
    } else {
      // 记录足够，使用真实记录
      rollingRecords.value = realRecords;
    }

    spinInfo.value.total_prize = data.total_prize || 0;
  } else {
    // 请求失败，使用模拟记录
    rollingRecords.value = generateTempRecords();
  }
};

// ==================== 事件处理函数 ====================
/**
 * 关闭弹窗
 */
const handleClose = (): void => {
  showSpinWheelTip.value = false;
  if (sourceSpinWheel.value === POP_FORM.AUTO) {
    AutoPopMgr.destroyCurrentPopup();
  }
};

/**
 * 点击底部按钮切换活动（根据 Cocos 逻辑）
 */
const btnsClick = (buttonData: ButtonData): void => {
  if (!spinData.value) return;

  // 检查是否满足投注要求
  if (buttonData.daily_betting_amount > spinData.value.bet_amount) {
    showToast(
      `Bet ₱${formatNumberToThousands(buttonData.daily_betting_amount, {
        precision: 0,
      })} to start ${buttonData.spin_activity_name}!`
    );
    return;
  }

  // 查找目标活动
  const targetActivity = spinData.value.spin_activity.find(
    (activity) => activity.spin_activity_id === buttonData.spin_activity_id
  );

  if (targetActivity) {
    updateTurntableData(targetActivity, spinData.value);
    updateSpinAvailability(spinData.value, targetActivity);
    showToast(`Please click the spin to start ${buttonData.spin_activity_name}`);
  }
};

/**
 * 禁用状态下的点击提示（根据 Cocos 逻辑）
 */
const spinDisableClick = (): void => {
  if (!spinData.value) return;

  // 检查首充状态
  if (!spinData.value.is_first_recharge) {
    showToast("Unlock this Spin by making First Deposit.");
    return;
  }

  // 检查是否已用完所有次数
  if (spinData.value.current_spin_activity_sort >= 5) {
    showToast("Spins all used up today. Try your luck again tomorrow!");
    return;
  }

  // 检查投注金额
  const currentActivity = getCurrentActivity();
  if (!currentActivity) return;

  if (spinData.value.bet_amount < currentActivity.daily_betting_amount) {
    const diff = MathUtils.subtract(
      currentActivity.daily_betting_amount,
      spinData.value.bet_amount
    );
    showToast(
      `Bet ₱${formatNumberToThousands(diff, { precision: 0 })} to start ${
        currentActivity.spin_activity_name
      }!`
    );
    return;
  }

  // 其他情况的通用提示
  showToast("Failed to obtain information.");
};

/**
 * 获取当前活动
 */
const getCurrentActivity = (): SpinActivity | null => {
  if (!spinData.value) return null;

  const currentIndex = Math.min(Math.max(spinData.value.current_spin_activity_sort, 0), 4);
  return spinData.value.spin_activity[currentIndex] || null;
};

// 监听弹窗显示状态，当弹窗显示时初始化数据
watch(showSpinWheelTip, (isVisible) => {
  if (isVisible) {
    initData();
  }
});

// 提前请求
watch(spinInfo, (spinInfo) => {
  if (spinInfo.is_start == 1 && globalStore.token) {
    initData();
  }
});

// ==================== 抽奖执行函数 ====================
let totalRotation = 0;

/**
 * 执行抽奖并获取结果
 */
const getSpinResult = async (): Promise<void> => {
  try {
    const response = await apiSpinResult({
      spin_activity_id: turntableData.value.spin_activity_id,
    });
    popEndTips(response);
    if (response?.code === 0) {
      const result = response.data as unknown as { spin_activity_prize_id: string | number };
      const targetSpinId = result.spin_activity_prize_id;
      const targetIdx = turntableData.value.spin_activity_prize_config.findIndex(
        (config) => config.spin_activity_prize_config_id === targetSpinId
      );

      if (targetIdx === -1) {
        throw new Error("Prize configuration not found");
      }

      // 计算旋转角度（每个奖项72度）
      const baseDegree = targetIdx * 72;
      totalRotation += 360 * 3 + (baseDegree - (totalRotation % 360));

      // 更新抽奖结果
      spinResult.value = {
        targetSpinId,
        targetIdx,
        rotateDeg: totalRotation,
      };

      // 更新剩余次数
      await autoPopMgrStore.getSpinInfo();

      // 重新加载转盘数据（与 Cocos 逻辑一致）
      await loadSpinData();
    }
  } catch (error) {
    console.error("Spin failed:", error);
    showToast("Spin failed, please try again");
  }
};
</script>
<style lang="scss" scoped>
.turntable-wrap {
  position: relative;
  transform: scale(0.9);

  .turntable-head {
    position: absolute;
    width: 100%;
    margin: 0 auto;
    z-index: 3;
    top: -25px;

    .head-bg {
      width: 90%;
      margin: 0 auto;
    }

    .type-text {
      position: absolute;
      top: 24vw;
      left: 32%;
      width: auto;
      height: 50px;
    }
  }

  .turntable-content {
    width: 100%;
    position: relative;

    img:first-child {
      width: 134%;
      max-width: 134% !important;
      margin-left: -15%;
      height: auto;
    }

    .rotate-arrow {
      position: absolute;
      width: 10%;
      top: 100px;
      left: 51%;
      z-index: 4;
      transform: translate(-40%, 0);
    }

    .rotate-img {
      position: absolute;
      top: 24%;
      width: 74%;
      height: 74vw;
      left: 15%;
      transition: transform 5s cubic-bezier(0.2, 0.8, 0.2, 1);
      background-image: url("@/assets/images/turnTable/turntable-content.png");
      background-repeat: no-repeat;
      background-size: cover;

      img {
        height: auto;
        width: 100%;
      }
    }

    .rotate-active {
      position: absolute;
      top: 117px;
      width: 49%;
      left: 27.5%;
    }
  }

  .turntable-progress {
    position: absolute;
    top: 116vw;
    left: 23%;
    width: 58%;
    height: 18px;
    margin: 0 auto;
    background: #250d00;
    border-radius: 100px;

    .progress {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 100px 0 0 100px;
      background: linear-gradient(to right, #d700e2, #0085ff);
    }

    .progress-text {
      line-height: 18px;
      color: #fff;
      font-size: 10px;
      position: absolute;
      z-index: 5;
      padding: 0 2px;
      font-family: D-DIN;
      text-align: center;
    }
  }

  .rotate-num {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    font-size: 18px;
    font-weight: 700;
    display: inline-block;
    width: 32%;
    text-align: center;
  }

  .rotate-num1 {
    /*  顶部 */
    top: 17%;
    left: 50%;
    transform: translate(-50%, 0);
    -webkit-text-stroke: 1px #a94e00;
    text-stroke: 1px #a94e00;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #a94e00, 0 0 2px #fff, 1px 1px 0 #a94e00, -1px -1px 0 #a94e00;
  }

  .rotate-num2 {
    /* 右上 */
    top: 37%;
    right: 6%;
    transform: rotate(65deg);
    -webkit-text-stroke: 1px #0060c0;
    text-stroke: 1px #0060c0;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #0060c0, 0 0 2px #fff, 1px 1px 0 #0060c0, -1px -1px 0 #a94e00;
  }

  .rotate-num3 {
    /* 右下 */
    bottom: 23%;
    right: 19%;
    transform: rotate(140deg);
    -webkit-text-stroke: 1px #93009b;
    text-stroke: 1px #93009b;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #93009b, 0 0 2px #fff, 1px 1px 0 #93009b, -1px -1px 0 #a94e00;
  }

  .rotate-num4 {
    /* 左下 */
    bottom: 24%;
    left: 18%;
    transform: rotate(-145deg);
    -webkit-text-stroke: 1px #966c00;
    text-stroke: 1px #966c00;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #966c00, 0 0 2px #fff, 1px 1px 0 #966c00, -1px -1px 0 #a94e00;
  }

  .rotate-num5 {
    /* 左上 */
    top: 37%;
    left: 8%;
    transform: rotate(-75deg);
    -webkit-text-stroke: 1px #569c00;
    text-stroke: 1px #569c00;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #569c00, 0 0 2px #fff, 1px 1px 0 #569c00, -1px -1px 0 #a94e00;
  }

  .turntable-rollNum {
    width: 74%;
    margin: 0 auto;
    position: absolute;
    bottom: 10px;
    left: 15%;
    font-size: 18px;
    color: #00f570;
    display: flex;
    justify-content: space-around;
    --van-rolling-text-color: #00f570;
    --van-rolling-text-font-size: 18px;
    --van-rolling-text-item-width: 10px;
    gap: 10px;

    .rollNum-title {
      font-size: 12px;
      color: #333;
      font-weight: 800;
      font-family: D-DIN;
    }

    .rolling-number {
      font-size: 14px;
      padding-top: 5px;
      // padding: 0;
    }

    > div {
      width: 50%;
      // padding-top: 16px;
      text-align: center;

      // margin-right: 8px;
      &:last-child {
        height: 13vw;
        padding-top: 0;
        margin-right: 0;
      }
    }
  }

  .rotate-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 110px;
    height: auto;
    transform: translate(-50%, -50%);
  }

  .turntable-btns {
    display: flex;
    position: relative;
    justify-content: center;
    gap: 0;
    margin-top: 6px;
    margin-left: 26px;
    height: 54px;

    .btns-overLay {
      display: block;
      position: absolute;
      top: 0;
      left: 5px;
      width: 93%;
      height: 100%;
      background: rgba(0, 4, 8, 0.5);

      .foot-btn {
        background: none !important;
      }
    }

    .foot-btn {
      position: absolute;
      height: 54px;
      display: flex;
      font-size: 12px;
      align-items: flex-end;
      color: #fff;
      text-align: center;
      justify-content: center;
      background-size: contain;
      background-repeat: no-repeat;
      padding-bottom: 3px;

      &.btn1 {
        left: 6px;
        width: 56px;
        background-image: url("../../assets/images/turnTable/btn-1.png");
        justify-content: start;
        padding-left: 8px;
      }

      &.active {
        z-index: 5;
      }

      &.btn2 {
        background-image: url("@/assets/images/turnTable/btn-2.png");
        width: 67px;
        left: 14.8%;
      }

      &.btn3 {
        background-image: url("@/assets/images/turnTable/btn-3.png");
        width: 71px;
        left: 31.3%;
      }

      &.btn4 {
        background-image: url("@/assets/images/turnTable/btn-4.png");
        width: 79px;
        left: 49.3%;
      }

      &.btn5 {
        background-image: url("@/assets/images/turnTable/btn-5.png");
        width: 86px;
        left: 69.6%;
      }
    }
  }

  .turntable-close {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
